import React, { Suspense, useMemo } from 'react';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { Activity } from 'app/components/ShiftManager/Activities/Activity/Activity';
import { StartupDialogs } from 'app/components/StartupDialogs/StartupDialogs';
import { EUA } from 'app/components/UI/EUA/EUA';
import { useOnboardingRedirect } from 'app/hooks/useOnboardingRedirect';
import { usePostLoginRedirect } from 'app/hooks/usePostLoginRedirect';
import { Email } from 'app/pages/myProfile/email/Email';
import { MyProfile } from 'app/pages/myProfile/MyProfile';
import { Name } from 'app/pages/myProfile/name/Name';
import { Password } from 'app/pages/myProfile/password/Password';
import { CookieDeclaration } from 'app/pages/myProfile/privacy/CookieDeclaration';
import { Notifications } from 'app/pages/notifications/Notifications';
import { Activities as NewActivities } from 'app/pages/projects/[projectId]/activities/Activities';
import { Activity as ActivityModal } from 'app/pages/projects/[projectId]/activities/Activity';
import { ActivityInsights } from 'app/pages/projects/[projectId]/activities/ActivityInsights';
import { ExpandedActivityDetails } from 'app/pages/projects/[projectId]/activities/ExpandedActivityDetails';
import { ExpandedActivityReadinessWidget } from 'app/pages/projects/[projectId]/activities/ExpandedActivityReadiness';
import { ExpandedDailyProgressWidget } from 'app/pages/projects/[projectId]/activities/ExpandedDailyProgressWidget';
import { ExpandedGalleryWidget } from 'app/pages/projects/[projectId]/activities/ExpandedGalleryWidget';
import { ExpandedResourcesUsedWidget } from 'app/pages/projects/[projectId]/activities/ExpandedResourcesUsedWidget';
import { ExpandedWeeklyPlanningWidget } from 'app/pages/projects/[projectId]/activities/ExpandedWeeklyPlanningWidget';
import { ChannelsLayout } from 'app/pages/projects/[projectId]/channels/_layout';
import { ChannelLayoutPage } from 'app/pages/projects/[projectId]/channels/[channelId]/_layout';
import { ChannelDetailsPage } from 'app/pages/projects/[projectId]/channels/[channelId]/details/index';
import { ChannelAddMembers } from 'app/pages/projects/[projectId]/channels/[channelId]/details/members/add';
import { ChannelPage } from 'app/pages/projects/[projectId]/channels/[channelId]/index';
import { ChannelsList } from 'app/pages/projects/[projectId]/channels/index';
import { NewChannelPage } from 'app/pages/projects/[projectId]/channels/new';
import { ChannelNewGroupLayout } from 'app/pages/projects/[projectId]/channels/new/group/_layout';
import { NewChannelGroupMembers } from 'app/pages/projects/[projectId]/channels/new/group/index';
import { NewChannelGroupInfoLayout } from 'app/pages/projects/[projectId]/channels/new/group/info/_layout';
import { NewChannelGroupInfo } from 'app/pages/projects/[projectId]/channels/new/group/info/index';
import { ControlCenter } from 'app/pages/projects/[projectId]/control-center/ControlCenter';
import { DataBookCategoryLayout } from 'app/pages/projects/[projectId]/data-book/DataBookCategoryLayout';
import { DataBookLayout } from 'app/pages/projects/[projectId]/data-book/DataBookLayout';
import { IssuesHealthHeatmap } from 'app/pages/projects/[projectId]/data-book/issue-tracker/health-heatmap/IssuesHealthHeatmap';
import { IssueTracker } from 'app/pages/projects/[projectId]/data-book/issue-tracker/IssueTracker';
import { DataBookLandingPage } from 'app/pages/projects/[projectId]/data-book/landing-page/DataBookLandingPage';
import { ShiftReportsHeatmap } from 'app/pages/projects/[projectId]/data-book/shift-manager/health-heatmap/ShiftReportsHeatmap';
import { ShiftManager } from 'app/pages/projects/[projectId]/data-book/shift-manager/ShiftManager';
import { ProjectGalleryLayout } from 'app/pages/projects/[projectId]/gallery/layout';
import { ProjectGallery } from 'app/pages/projects/[projectId]/gallery/ProjectGallery';
import { IssueExport } from 'app/pages/projects/[projectId]/issues/[issueId]/export/IssueExport';
import { Issue } from 'app/pages/projects/[projectId]/issues/[issueId]/Issue';
import { IssueLayout } from 'app/pages/projects/[projectId]/issues/[issueId]/IssueLayout';
import { Issues } from 'app/pages/projects/[projectId]/issues/Issues';
import { NewIssue } from 'app/pages/projects/[projectId]/issues/new/NewIssue';
import { ModalProjectLayout } from 'app/pages/projects/[projectId]/ModalProjectLayout';
import { ProjectLayout } from 'app/pages/projects/[projectId]/ProjectLayout';
import { Search } from 'app/pages/projects/[projectId]/search/Search';
import { Timeline } from 'app/pages/projects/[projectId]/timeline/Timeline';
import { TimelineV2 } from 'app/pages/projects/[projectId]/timelineV2/TimelineV2';
import { Lookback } from 'app/pages/projects/[projectId]/weekly-planner/[planId]/lookback/Lookback';
import { NewPlan } from 'app/pages/projects/[projectId]/weekly-planner/new/NewPlan';
import { Plan } from 'app/pages/projects/[projectId]/weekly-planner/plan/Plan';
import { PlanLayout } from 'app/pages/projects/[projectId]/weekly-planner/plan/PlanLayout';
import { ProgressLogs } from 'app/pages/projects/[projectId]/weekly-planner/plan/ProgressLogs';
import { WeeklyPlanner } from 'app/pages/projects/[projectId]/weekly-planner/WeeklyPlanner';
import { useCurrentUser } from 'app/queries/users/users';
import { Activities } from 'app/screens/Project/ProjectShiftManager/Activities/Activities';
import { ArchivedActivities } from 'app/screens/Project/ProjectShiftManager/Activities/ArchivedActivities/ArchivedActivities';
import { NewActivity } from 'app/screens/Project/ProjectShiftManager/Activities/NewActivity/NewActivity';
import { ProjectShiftManager } from 'app/screens/Project/ProjectShiftManager/ProjectShiftManager';
import { ArchivedShiftReports } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ArchivedShiftReports/ArchivedShiftReports';
import { DraftShiftReports } from 'app/screens/Project/ProjectShiftManager/ShiftReports/DraftShiftReports/DraftShiftReports';
import { EditShiftReport } from 'app/screens/Project/ProjectShiftManager/ShiftReports/EditShiftReport/EditShiftReport';
import { PublishedShiftReports } from 'app/screens/Project/ProjectShiftManager/ShiftReports/PublishedShiftReports/PublishedShiftReports';
import { ShiftReportLayout } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportLayout';
import { ShiftReportsManager } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportsManager/ShiftReportsManager';
import { ViewShiftReport } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ViewShiftReport/ViewShiftReport';
import { ViewShiftReportCollaboratorsComms } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ViewShiftReport/ViewShiftReportCollaboratorsComms';
import { ViewShiftReportPage } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ViewShiftReport/ViewShiftReportPage';
import { ViewShiftReportPublicComms } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ViewShiftReport/ViewShiftReportPublicComms';
import { generatePath, Navigate, Route, Routes, useLocation, useParams } from 'react-router';
import { ChannelInvitePage } from './channels/invite/[inviteId]';
import { ArchivedProjectsList } from './myProjects/archivedProjects/ArchivedProjectsList';
import { MyProjects } from './myProjects/MyProjects';
import { MyProjectsLayout } from './myProjects/MyProjectsLayout';
import { NewProject } from './myProjects/new/NewProject';
import { OnboardingPage } from './onboarding';
import { OnboardingFinishPage } from './onboarding/finish';
import { OnboardingLayout } from './onboarding/layout';
import { ExternalDashboardLayout } from './projects/[projectId]/data-book/[category]/[dashboardId]/ExternalDashboardLayout';
import { ExternalDashboardPage } from './projects/[projectId]/data-book/[category]/[dashboardId]/ExternalDashboardPage';
import { CustomDashboard } from './projects/[projectId]/data-book/custom-dashboard/CustomDashboard';
import { QuickCapture } from './projects/[projectId]/issues/quickCapture/QuickCapture';
import { AccessRequests } from './projects/[projectId]/settings/access-requests/AccessRequests';
import { CustomFields } from './projects/[projectId]/settings/custom-fields/CustomFields';
import { EditCustomField } from './projects/[projectId]/settings/custom-fields/components/EditCustomField';
import { NewCustomField } from './projects/[projectId]/settings/custom-fields/components/NewCustomField';
import { Disciplines } from './projects/[projectId]/settings/disciplines/Disciplines';
import { Locations } from './projects/[projectId]/settings/locations/Locations';
import { ProjectSettings } from './projects/[projectId]/settings/project';
import { Resources } from './projects/[projectId]/settings/resources/Resources';
import { Settings } from './projects/[projectId]/settings/Settings';
import { Setup } from './projects/[projectId]/settings/teams/[teamId]/setup/Setup';
import { Confirmation } from './projects/[projectId]/settings/teams/[teamId]/subscription/confirmation/Confirmation';
import { Team } from './projects/[projectId]/settings/teams/[teamId]/Team';
import { Invite } from './projects/[projectId]/settings/teams/invite/Invite';
import { Teams } from './projects/[projectId]/settings/teams/Teams';

/** Naviates to the current route with the background pathname added to history state.
 * @note The background route can only use parameters that already exist on the current route.
 */
const NavigateBackground = ({ background }: { background: string }) => {
  const location = useLocation();
  const params = useParams();
  const bg = useMemo(() => generatePath(background, params), [background, params]);

  return <Navigate replace to={location.pathname + location.search} state={{ background: { pathname: bg } }} />;
};

const PrivateRoutes = () => {
  const { value: isTimelineV2 } = useFeatureFlag('timeline-v2');
  const { value: isDataBookProEnabled } = useFeatureFlag('data-book-pro');
  const currentUser = useCurrentUser();
  const location = useLocation();
  const { postLoginPath } = usePostLoginRedirect();

  const background = location.state?.background;

  useOnboardingRedirect();

  if (currentUser.pendingEuaAcceptance) return <EUA />;

  return (
    <>
      <Routes location={background || location}>
        <Route path="projects/:projectId" element={<ProjectLayout />}>
          <Route index element={<Navigate replace to="issues" />} />

          <Route path="search" element={<Search />} />

          <Route path="issues">
            <Route index element={<Navigate to="lists" replace />} />
            <Route path="lists">
              <Route index element={<Navigate to="all" replace />} />
              <Route path=":tabId" element={<Issues />} />
            </Route>

            <Route path=":issueId" element={<IssueLayout />}>
              <Route index element={<Issue />} />
              <Route path="export" element={<IssueExport />} />
            </Route>
          </Route>

          {!isTimelineV2 && <Route path="timeline" element={<Timeline />} />}
          {isTimelineV2 && <Route path="timeline" element={<TimelineV2 />} />}

          <Route path="control-center" element={<ControlCenter />} />

          <Route path="shift-reports" element={<ProjectShiftManager />}>
            <Route index element={<PublishedShiftReports />} />
            <Route path="drafts" element={<DraftShiftReports />} />
            <Route path="archived" element={<ArchivedShiftReports />} />
            <Route path="manager" element={<ShiftReportsManager />} />
          </Route>

          <Route path="activities" element={<NewActivities />}>
            <Route index element={<Activities />} />
            <Route path="archived" element={<ArchivedActivities />} />
            <Route path=":shiftActivityId" element={<ActivityModal />}>
              <Route index element={<ActivityInsights />} />
              <Route path="details" element={<ExpandedActivityDetails />} />
              <Route path="readiness" element={<ExpandedActivityReadinessWidget />} />
              <Route path="resources-used" element={<ExpandedResourcesUsedWidget />} />
              <Route path="weekly-planning" element={<ExpandedWeeklyPlanningWidget />} />
              <Route path="daily-progress" element={<ExpandedDailyProgressWidget />} />
              <Route path="gallery" element={<ExpandedGalleryWidget />} />
            </Route>
            <Route path=":shiftActivityId/edit" element={<Activity />} />
          </Route>

          <Route path="shift-reports/:shiftReportId" element={<ShiftReportLayout />}>
            <Route
              element={
                <Suspense fallback={<LoadingSpinner variant="screen" />}>
                  <ViewShiftReport />
                </Suspense>
              }
            >
              <Route index element={<ViewShiftReportPage />} />
              <Route path="collaborators" element={<ViewShiftReportCollaboratorsComms />} />
              <Route path="public" element={<ViewShiftReportPublicComms />} />
            </Route>
            <Route path="edit" element={<EditShiftReport />} />
          </Route>

          <Route path="weekly-planner/plans">
            <Route index element={<Navigate to="lists" replace />} />
            <Route path="lists">
              <Route index element={<Navigate to="my-plans" replace />} />
              <Route path=":tabId" element={<WeeklyPlanner />} />
            </Route>
          </Route>

          <Route
            path="weekly-planner/plans/:planId"
            element={
              <Suspense fallback={<LoadingSpinner variant="screen" />}>
                <PlanLayout />
              </Suspense>
            }
          >
            <Route index element={<Plan />} />
            <Route path="progress-logs" element={<ProgressLogs />} />
            <Route path="lookback" element={<Lookback />} />
          </Route>

          <Route path="channels" element={<ChannelsLayout />}>
            <Route path="" element={<ChannelsList />}>
              <Route path="new" element={<NewChannelPage />} />

              <Route path="new/group" element={<ChannelNewGroupLayout />}>
                <Route path="" element={<NewChannelGroupMembers />} />
                <Route path="info" element={<NewChannelGroupInfoLayout />}>
                  <Route index element={<NewChannelGroupInfo />} />
                </Route>
              </Route>

              <Route path=":channelId" element={<ChannelLayoutPage />}>
                <Route index element={<ChannelPage />} />
                <Route path="details" element={<ChannelDetailsPage />} />
                <Route path="details/members/add" element={<ChannelAddMembers />} />
              </Route>
            </Route>
          </Route>

          <Route path="gallery" element={<ProjectGalleryLayout />}>
            <Route path="" element={<ProjectGallery />}>
              <Route path=":documentId" element={null} />
            </Route>
          </Route>

          <Route path="data-book" element={<DataBookLayout />}>
            <Route index element={<DataBookLandingPage />} />
            <Route path="shift-manager" element={<DataBookCategoryLayout />}>
              <Route index element={<ShiftManager />} />
              <Route path="heatmap" element={<ShiftReportsHeatmap />} />
            </Route>
            <Route path="issue-tracker" element={<DataBookCategoryLayout />}>
              <Route index element={<IssueTracker />} />
              <Route path="heatmap" element={<IssuesHealthHeatmap />} />
            </Route>
            {isDataBookProEnabled && (
              <Route path="custom" element={<DataBookCategoryLayout />}>
                <Route index element={<CustomDashboard />} />
              </Route>
            )}
            <Route path=":category/:dashboardId" element={<ExternalDashboardLayout />}>
              <Route index element={<ExternalDashboardPage />} />
            </Route>
          </Route>

          <Route path="settings" element={<Settings />}>
            <Route path="custom-fields" element={<CustomFields />}>
              <Route path="new" element={<NewCustomField />} />
              <Route path=":customFieldId/edit" element={<EditCustomField />} />
            </Route>

            <Route path="teams" element={<Teams />} />
            <Route path="teams/invite" element={<Invite />} />
            <Route path="teams/:teamId">
              <Route index element={<Navigate to="joined" replace />} />
              <Route path=":tabId" element={<Team />} />
              <Route path="setup" element={<Setup />} />
              <Route path="subscription/confirmation" element={<Confirmation />} />
            </Route>

            <Route path="locations" element={<Locations />} />

            <Route path="disciplines" element={<Disciplines />} />

            <Route path="access-requests" element={<AccessRequests />} />

            <Route path="resources">
              <Route index element={<Navigate to="person" replace />} />
              <Route path=":resourceKind" element={<Resources />} />
            </Route>

            <Route path="project" element={<ProjectSettings />} />
          </Route>
        </Route>

        <Route path="/auth" element={<Navigate to={postLoginPath} />} />
        <Route path="/signup" element={<Navigate to={postLoginPath} />} />

        <Route path="/my-profile" element={<MyProfile />}>
          <Route path="/my-profile/password" element={<Password />} />
          <Route path="/my-profile/name" element={<Name />} />
          <Route path="/my-profile/email" element={<Email />} />
          <Route path="/my-profile/cookie-declaration" element={<CookieDeclaration />} />
        </Route>

        <Route path="/notifications" element={<Notifications />} />

        <Route path="/my-projects" element={<MyProjectsLayout />}>
          <Route index element={<MyProjects />} />
          <Route path="/my-projects/archived" element={<ArchivedProjectsList />} />
        </Route>

        <Route path="/channels/invite/:inviteId" element={<ChannelInvitePage />} />

        {currentUser.onboardingState === 'in_progress' && (
          <Route path="/onboarding" element={<OnboardingLayout />}>
            <Route index element={<OnboardingPage />} />
          </Route>
        )}

        {/* Redirect modal links to add `background` in history state.
        This is required to allow using plain urls to access the modal routes */}
        <>
          <Route path="/my-projects/new" element={<NavigateBackground background="/my-projects" />} />
          <Route
            path="/projects/:projectId/activities/new"
            element={<NavigateBackground background="/projects/:projectId/activities" />}
          />
          <Route
            path="/projects/:projectId/issues/new"
            element={<NavigateBackground background="/projects/:projectId/issues/lists/all" />}
          />
          <Route
            path="/projects/:projectId/issues/quick-capture"
            element={<NavigateBackground background="/projects/:projectId/issues/lists/all" />}
          />
          <Route
            path={'/projects/:projectId/weekly-planner/plans/:planId/activity/:shiftActivityId'}
            element={<NavigateBackground background="/projects/:projectId/weekly-planner/plans/:planId" />}
          />
          <Route
            path={'/projects/:projectId/weekly-planner/plans/:planId/lookback/activity/:shiftActivityId'}
            element={<NavigateBackground background="/projects/:projectId/weekly-planner/plans/:planId/lookback" />}
          />
          <Route
            path="/projects/:projectId/weekly-planner/plans/new"
            element={<NavigateBackground background="/projects/:projectId/weekly-planner/plans/lists/my-plans" />}
          />
          <Route path="/onboarding/finish" element={<NavigateBackground background="/my-projects" />} />
        </>

        <Route path="*" element={<Navigate to={postLoginPath} />} />
      </Routes>
      <StartupDialogs />
      {background && (
        <Routes location={location}>
          <Route path="/my-projects/new" element={<NewProject />} />
          <Route path="/onboarding/finish" element={<OnboardingFinishPage />} />

          <Route element={<ModalProjectLayout />}>
            <Route path="/projects/:projectId/issues/new" element={<NewIssue />} />
            <Route path="/projects/:projectId/issues/quick-capture" element={<QuickCapture />} />
            <Route path="/projects/:projectId/activities/:shiftActivityId" element={<ActivityModal />}>
              <Route index element={<ActivityInsights />} />
            </Route>
            <Route
              path={'/projects/:projectId/weekly-planner/plans/:planId/activity/:shiftActivityId'}
              element={<ActivityModal />}
            >
              <Route index element={<ActivityInsights />} />
              <Route path="readiness" element={<ExpandedActivityReadinessWidget />} />
            </Route>
            <Route
              path={'/projects/:projectId/weekly-planner/plans/:planId/lookback/activity/:shiftActivityId'}
              element={<ActivityModal />}
            >
              <Route index element={<ActivityInsights />} />
            </Route>
            <Route path="/projects/:projectId/activities/:shiftActivityId/edit" element={<Activity />} />
            <Route path="/projects/:projectId/activities/new" element={<NewActivity />} />
            <Route path="/projects/:projectId/weekly-planner/plans/new" element={<NewPlan />} />
          </Route>
        </Routes>
      )}
    </>
  );
};

export default PrivateRoutes;
