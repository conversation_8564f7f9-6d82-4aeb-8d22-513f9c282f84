import { booleanFlag, config } from './src/utils/configHelpers';

/**
 * Feature flags configuration.
 *
 * @see {import('./types').FlagConfig} for more information on the configuration options.
 *
 * @example
 * ## For boolean flags -
 * ```js
 * config({
 *   // ...
 *   dark_mode: booleanFlag({ expiry: '2023-12-15' }),
 * })
 * ```
 *
 * ## For variant flags -
 * ```js
 * config({
 *   // ...
 *   new_issue_redesign: variantFlag<'A' | 'B'>({ // Variant keys for better type information
 *     target: 'team', // Targets team for gradual rollout and A/B
 *     default: 'B', // Default value for flag
 *     expiry: '2023-12-15',
 *   }),
 * })
 * ```
 */
export const FEATURE_FLAGS_CONFIG = config({
  'timeline-v2': booleanFlag({
    expiry: '2025-10-16',
  }),
  'export-change': booleanFlag({
    expiry: '2025-10-15',
  }),
  'streamlining-progress-logs': booleanFlag({
    expiry: '2025-10-22',
  }),
  'smart-issue': booleanFlag({
    expiry: '2025-10-22',
  }),
  'data-book-pro': booleanFlag({
    expiry: '2025-10-30',
  }),
  'shift-report-approvals': booleanFlag({
    expiry: '2025-10-20',
  }),
});

export type FlagName = keyof typeof FEATURE_FLAGS_CONFIG;
